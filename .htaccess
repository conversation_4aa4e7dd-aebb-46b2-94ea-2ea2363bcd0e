# 缓存策略配置文件

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 设置缓存头
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 图片缓存1年
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS和JS缓存1个月
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # HTML缓存1小时
    ExpiresByType text/html "access plus 1 hour"
    
    # 字体缓存1年
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
</IfModule>

# 设置Cache-Control头
<IfModule mod_headers.c>
    # 静态资源设置强缓存
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|ico|woff|woff2)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    # HTML设置协商缓存
    <FilesMatch "\.html$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </FilesMatch>
    
    # JSON文件缓存
    <FilesMatch "\.json$">
        Header set Cache-Control "public, max-age=86400"
    </FilesMatch>
</IfModule>

# 启用ETag
FileETag MTime Size

# 安全头设置
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# 重写规则
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # 强制HTTPS（如果需要）
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # 移除www前缀（如果需要）
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
</IfModule>
