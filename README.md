# 🌊 鸣潮词条分数计算器

一个专为《鸣潮》游戏设计的词条分数计算工具，帮助玩家快速评估声骸词条的价值。

![鸣潮词条分数计算器](https://pic1.imgdb.cn/item/6885d68758cb8da5c8e446a6.png)

## ✨ 功能特性

### 🎯 核心功能
- **词条分数计算**: 根据词条数值和权重自动计算分数
- **角色模板系统**: 内置40+角色的推荐配置模板
- **权重模板系统**: 提供通用权重模板（输出、辅助、坦克等）
- **多费位支持**: 支持1费位、3费位、4费位的不同配置
- **实时计算**: 输入数值后实时显示分数和总分
- **📷 OCR图片识别**: 支持上传声骸截图，自动识别词条数值
- **📋 历史记录**: 自动保存OCR识别记录，支持快速应用历史数据



## 🚀 快速开始

### 环境准备

#### 使用UV（推荐）
本项目已配置使用[uv](https://github.com/astral-sh/uv)进行Python环境管理。

1. **安装uv**：
   ```bash
   # Windows (PowerShell)
   irm https://astral.sh/uv/install.ps1 | iex
   
   # macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **初始化项目**：
   ```bash
   # 进入项目目录
   cd wwuid词条分数计算器
   
   # 创建虚拟环境并安装所有依赖
   uv sync
   
   # 激活虚拟环境
   uv venv activate
   ```

3. **运行项目**：
   ```bash
   uv run python server.py
   # 或指定端口
   uv run python server.py 8001
   ```

#### 传统方式（备用）
1. 克隆或下载项目文件
2. 配置OCR：
   - 复制 `ocr-config.json.example` 为 `ocr-config.json`
   - 配置OCR提供商（可选择百度OCR、PaddleOCR或同时启用）
   - 设置优先级和启用状态
3. 安装Python依赖：
   ```bash
   pip install -r requirements.txt
   ```
4. 启动Python服务器：
   ```bash
   python server.py
   ```
5. 在浏览器中访问 `http://localhost:8000`

## 📖 使用指南



### 角色模板使用
1. **选择角色**: 在右侧面板选择目标角色
2. **选择费位**: 点击C1/C3/C4按钮切换费位
3. **预览配置**: 查看推荐的主词条和副词条权重
4. **应用模板**: 点击"应用模板"按钮一键应用配置

### 📷 OCR图片识别
1. **打开OCR功能**: 点击页面右上角的"📷 OCR识别"按钮
2. **上传图片**: 
   - 拖拽声骸截图到上传区域
   - 或点击"选择图片"按钮选择文件
   - 支持JPG、PNG、GIF格式
3. **开始识别**: 点击"🔍 开始识别"按钮
4. **查看结果**: 系统自动识别词条并显示结果
5. **应用数据**: 点击"应用到表格"将识别结果填入计算表格

### 📋 历史记录管理
1. **查看历史**: 在OCR弹窗中点击"📋 历史记录"标签页
2. **历史记录信息**: 
   - 显示识别时间
   - 显示词条数量
   - 显示具体属性和数值
3. **快速应用**: 点击历史记录的"应用"按钮快速填入数据
4. **删除记录**: 点击"删除"按钮移除单条记录
5. **清空历史**: 点击"清空历史"按钮清除所有记录
6. **自动保存**: 系统自动保存最近50条识别记录

### ⚙️ OCR配置说明
系统支持多种OCR引擎，可在 `ocr-config.json` 中配置：

#### 配置示例
```json
{
    "ocr_providers": [
        {
            "name": "baidu",
            "enabled": true,
            "priority": 1,
            "config": {
                "apiKey": "your_baidu_api_key",
                "secretKey": "your_baidu_secret_key"
            }
        },
        {
            "name": "paddleocr",
            "enabled": true,
            "priority": 2,
            "config": {
                "use_angle_cls": true,
                "use_gpu": false,
                "lang": "ch"
            }
        }
    ],
    "fallback_enabled": true,
    "timeout": 30
}
```

#### 配置说明
- **enabled**: 是否启用该OCR提供商
- **priority**: 优先级（数字越小优先级越高）
- **fallback_enabled**: 是否启用自动切换（第一个失败时使用第二个）
- **百度OCR**: 需要API密钥，识别精度高，有调用限制
- **PaddleOCR**: 本地识别，无需网络，免费使用

## 📊 计算公式

```
词条得分 = (词条数值 × 当前词条权重 ÷ 未对齐最高分) × 对齐分数
```

### 参数说明
- **词条数值**: 声骸上显示的具体数值
- **当前词条权重**: 该属性在当前角色配置下的重要程度
- **未对齐最高分**: 理论最高分数（默认100分）
- **对齐分数**: 标准化分数（默认50分）



## 🛠️ 技术栈

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **样式**: 原生CSS，响应式设计
- **数据**: JSON格式的角色配置文件
- **OCR识别**: 支持多种OCR引擎（百度OCR API、PaddleOCR），可配置优先级和自动切换
- **数据存储**: localStorage本地存储历史记录
- **后端**: Python Flask服务器（用于OCR API调用）
- **部署**: 静态文件 + Python服务器

## 📁 项目结构

```
wwscore/
├── index.html              # 主页面
├── script.js               # 核心JavaScript逻辑
├── style.css               # 样式文件
├── config.js               # 配置文件（角色列表等）
├── server.py               # Python服务器（OCR API调用）
├── ocr-config.json         # OCR配置文件
├── ocr-config.json.example # OCR配置文件示例
├── requirements.txt        # Python依赖文件（传统方式）
├── pyproject.toml          # UV项目配置
├── uv.lock                 # UV依赖锁定文件
├── .python-version         # Python版本指定
├── UV_SETUP.md             # UV使用指南
├── character/              # 角色配置目录
│   ├── 角色名/
│   │   └── calc.json       # 角色配置文件
└── README.md               # 项目说明文档
```



## 🙏 鸣谢

- **计算公式和权重数据**: 来自 [WutheringWavesUID (wwuid)](https://github.com/tyql688/WutheringWavesUID)
- **游戏资源**: 《鸣潮》官方
- **图标资源**: 游戏内图标

## 📄 许可证

本项目仅供学习和个人使用，请勿用于商业用途。

## 🐛 问题反馈

如果您在使用过程中遇到问题或有改进建议，欢迎提出Issue。



**享受游戏，合理配装！** 🎮✨
