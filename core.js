// 核心计算功能 - 压缩版
const defaultWeights={main:{攻击力:1,生命值:1,防御力:1,暴击:1,暴击伤害:1,共鸣效率:1,属性伤害:1},sub:{攻击力:1,生命值:1,防御力:1,暴击:1,暴击伤害:1,共鸣效率:1,属性伤害:1}};let weights=JSON.parse(JSON.stringify(defaultWeights)),maxScore=100,alignmentScore=50;function initApp(){document.getElementById('maxScoreMain').value=maxScore;document.getElementById('alignScoreMain').value=alignmentScore;createTableEntries();calculateTotal()}function createTableEntries(){const tbody=document.getElementById('entriesTableBody');tbody.innerHTML='';for(let i=1;i<=2;i++){const row=createTableRow(`main-${i}`,`主词条 ${i}`,'main');tbody.appendChild(row)}for(let i=1;i<=5;i++){const row=createTableRow(`sub-${i}`,`副词条 ${i}`,'sub');tbody.appendChild(row)}}function createTableRow(id,label,type){const row=document.createElement('tr');row.innerHTML=`<td>${label}</td><td><select class="table-select" id="${id}-type" onchange="calculateTotal()"><option value="">请选择</option><option value="攻击力">攻击力</option><option value="生命值">生命值</option><option value="防御力">防御力</option><option value="暴击">暴击</option><option value="暴击伤害">暴击伤害</option><option value="共鸣效率">共鸣效率</option><option value="属性伤害">属性伤害</option></select></td><td><input type="number" class="table-input" id="${id}-value" step="0.1" oninput="calculateTotal()" placeholder="0"></td><td><input type="number" class="table-input" id="${id}-weight" step="0.1" oninput="calculateTotal()" placeholder="1.0"></td><td><span id="${id}-score">0.00</span></td>`;return row}function calculateTotal(){let total=0;['main-1','main-2','sub-1','sub-2','sub-3','sub-4','sub-5'].forEach(id=>{const typeEl=document.getElementById(`${id}-type`),valueEl=document.getElementById(`${id}-value`),weightEl=document.getElementById(`${id}-weight`),scoreEl=document.getElementById(`${id}-score`);if(typeEl&&valueEl&&weightEl&&scoreEl){const type=typeEl.value,value=parseFloat(valueEl.value)||0,weight=parseFloat(weightEl.value)||0;let score=0;if(type&&value>0&&weight>0){score=(value*weight/maxScore)*alignmentScore}scoreEl.textContent=score.toFixed(2);total+=score}});document.getElementById('totalScore').textContent=total.toFixed(2)}function updateMaxScore(){maxScore=parseFloat(document.getElementById('maxScoreMain').value)||100;calculateTotal()}function updateAlignmentScore(){alignmentScore=parseFloat(document.getElementById('alignScoreMain').value)||50;calculateTotal()}document.addEventListener('DOMContentLoaded',function(){console.log('DOM加载完成，开始初始化应用...');initApp();document.body.classList.add('loaded')});
