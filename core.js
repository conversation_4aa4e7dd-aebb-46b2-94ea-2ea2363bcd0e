// 核心计算功能 - 修复版

// 属性类型配置
const mainAttributeTypes1 = {
    '': '请选择',
    'atk_percent': '攻击百分比',
    'crit_rate': '暴击',
    'crit_damage': '暴击伤害',
    'hp_percent': '生命百分比',
    'def_percent': '防御百分比',
    'healing_bonus': '治疗效果加成',
    'resonance_efficiency': '共鸣效率',
    'element_damage': '属性伤害加成'
};

const mainAttributeTypes2 = {
    '': '请选择',
    'atk': '攻击',
    'hp': '生命'
};

const subAttributeTypes = {
    '': '请选择',
    'atk': '攻击',
    'atk_percent': '攻击百分比',
    'hp': '生命',
    'hp_percent': '生命百分比',
    'def': '防御',
    'def_percent': '防御百分比',
    'crit_rate': '暴击',
    'crit_damage': '暴击伤害',
    'resonance_efficiency': '共鸣效率',
    'basic_attack_damage': '普攻伤害加成',
    'heavy_attack_damage': '重击伤害加成',
    'resonance_skill_damage': '共鸣技能伤害加成',
    'resonance_liberation_damage': '共鸣解放伤害加成'
};

// 默认权重配置
const defaultWeights = {
    main: {
        'hp': 0.1, 'hp_percent': 0.2, 'atk': 0.1, 'atk_percent': 0.3,
        'def': 0.05, 'def_percent': 0.1, 'resonance_efficiency': 0.2,
        'crit_rate': 0.5, 'crit_damage': 0.4, 'element_damage': 0.3,
        'healing_bonus': 0.2, 'basic_attack_damage': 0.3,
        'heavy_attack_damage': 0.3, 'resonance_skill_damage': 0.3,
        'resonance_liberation_damage': 0.3
    },
    sub: {
        'hp': 0.1, 'hp_percent': 0.3, 'atk': 0.15, 'atk_percent': 0.8,
        'def': 0.05, 'def_percent': 0.1, 'resonance_efficiency': 0.4,
        'crit_rate': 1.5, 'crit_damage': 1.0, 'element_damage': 0.5,
        'healing_bonus': 0.3, 'basic_attack_damage': 0.8,
        'heavy_attack_damage': 0.8, 'resonance_skill_damage': 0.8,
        'resonance_liberation_damage': 0.8
    }
};

let weights = JSON.parse(JSON.stringify(defaultWeights));
let maxScore = 100;
let alignmentScore = 50;

function initApp() {
    document.getElementById('maxScoreMain').value = maxScore;
    document.getElementById('alignScoreMain').value = alignmentScore;
    createTableEntries();
    calculateTotal();
}

function createTableEntries() {
    const tbody = document.getElementById('entriesTableBody');
    tbody.innerHTML = '';

    for (let i = 1; i <= 2; i++) {
        const row = createTableRow(`main-${i}`, `主词条 ${i}`, 'main');
        tbody.appendChild(row);
    }

    for (let i = 1; i <= 5; i++) {
        const row = createTableRow(`sub-${i}`, `副词条 ${i}`, 'sub');
        tbody.appendChild(row);
    }
}

function createTableRow(id, title, type) {
    let attributeOptions;
    if (type === 'main') {
        attributeOptions = id === 'main-1' ? mainAttributeTypes1 : mainAttributeTypes2;
    } else {
        attributeOptions = subAttributeTypes;
    }

    const shortTitle = title.replace('主词条', '主').replace('副词条', '副');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <span class="entry-position ${type}">
                <span class="full-text">${title}</span>
                <span class="short-text">${shortTitle}</span>
            </span>
        </td>
        <td>
            <select id="${id}-attribute" class="table-select" onchange="handleAttributeChange('${id}')">
                ${Object.entries(attributeOptions).map(([value, text]) =>
                    `<option value="${value}">${text}</option>`
                ).join('')}
            </select>
        </td>
        <td>
            <input type="number" id="${id}-value" class="table-input"
                   step="0.1" min="0" placeholder="输入数值"
                   onchange="updateEntry('${id}')">
        </td>
        <td>
            <input type="number" id="${id}-weight" class="table-input weight-editable"
                   step="0.1" min="0" placeholder="权重" value="0"
                   onchange="updateEntry('${id}')"
                   title="一次性权重调整，不会保存">
        </td>
        <td>
            <span id="${id}-result" class="entry-score">0.00</span>
        </td>
    `;
    return row;
}

function handleAttributeChange(entryId) {
    const attributeSelect = document.getElementById(`${entryId}-attribute`);
    const weightInput = document.getElementById(`${entryId}-weight`);
    const selectedAttribute = attributeSelect.value;

    if (!selectedAttribute) {
        weightInput.value = 0;
    } else {
        const isMainAttribute = entryId.startsWith('main-');
        const weightType = isMainAttribute ? 'main' : 'sub';
        const defaultWeight = defaultWeights[weightType][selectedAttribute];
        weightInput.value = defaultWeight !== undefined ? defaultWeight : 0;
    }

    updateEntry(entryId);
}

function updateEntry(entryId) {
    const attributeSelect = document.getElementById(`${entryId}-attribute`);
    const valueInput = document.getElementById(`${entryId}-value`);
    const weightInput = document.getElementById(`${entryId}-weight`);
    const resultSpan = document.getElementById(`${entryId}-result`);

    const attribute = attributeSelect.value;
    const value = parseFloat(valueInput.value) || 0;
    const weight = parseFloat(weightInput.value) || 0;

    const score = calculateEntryScore(attribute, value, weight);
    resultSpan.textContent = score.toFixed(2);
    calculateTotal();
}

function calculateEntryScore(attribute, value, weight) {
    if (!attribute || value <= 0 || weight <= 0) return 0;
    return (value * weight / maxScore) * alignmentScore;
}

function calculateTotal() {
    let total = 0;
    ['main-1', 'main-2', 'sub-1', 'sub-2', 'sub-3', 'sub-4', 'sub-5'].forEach(id => {
        const resultSpan = document.getElementById(`${id}-result`);
        if (resultSpan) {
            total += parseFloat(resultSpan.textContent) || 0;
        }
    });
    document.getElementById('totalScore').textContent = total.toFixed(2);
}

function updateMaxScore() {
    maxScore = parseFloat(document.getElementById('maxScoreMain').value) || 100;
    calculateTotal();
}

function updateAlignmentScore() {
    alignmentScore = parseFloat(document.getElementById('alignScoreMain').value) || 50;
    calculateTotal();
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化应用...');
    initApp();
    document.body.classList.add('loaded');
});
