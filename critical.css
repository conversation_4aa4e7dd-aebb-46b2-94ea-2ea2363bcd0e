/* 关键CSS - 首屏必需样式 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础布局 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: url('bg.webp');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    min-height: 100vh;
    color: #333;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px 20px 15px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 0;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
    flex: 1;
}

/* 头部样式 */
header {
    position: relative;
    text-align: center;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

header h1 {
    font-size: 1.8em;
    margin-bottom: 5px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header p {
    font-size: 0.9em;
    color: #444;
}

/* 图片样式 */
.header-main img {
    width: 50px;
    height: 50px;
    vertical-align: middle;
}

.template-icon img {
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-right: 8px;
}

.template-entry-icon img {
    width: 32px;
    height: 32px;
}

.footer-beian-icon {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.content-wrapper {
    display: flex;
    gap: 15px;
    flex: 1;
    min-height: 0;
}

.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.right-panel {
    width: 300px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 公式区域 */
.formula-section {
    margin-bottom: 15px;
    flex-shrink: 0;
}

.formula-box {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.formula-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.formula-header h2 {
    font-size: 1.2em;
    color: #333;
    margin: 0;
}

.formula {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    min-width: 300px;
}

/* 移动端基础适配 */
@media (max-width: 768px) {
    .container {
        padding: 5px;
    }
    
    header {
        padding: 10px;
    }
    
    header h1 {
        font-size: 1.3em;
    }
    
    .header-main img {
        width: 35px !important;
        height: 35px !important;
    }
    
    .content-wrapper {
        flex-direction: column;
        gap: 10px;
    }
    
    .left-panel,
    .right-panel {
        width: 100%;
    }
    
    .formula {
        font-size: 11px;
        min-width: auto;
        word-break: break-all;
    }
}

/* 加载动画 */
.loading {
    opacity: 0;
    animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* 隐藏非关键内容直到完全加载 */
.template-section,
.table-section {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.loaded .template-section,
.loaded .table-section {
    opacity: 1;
}
