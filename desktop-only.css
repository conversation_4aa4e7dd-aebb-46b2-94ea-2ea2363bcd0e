/* 桌面端专属样式：后续把仅桌面端使用的规则逐步迁移到这里 */
/* 横屏模式优化 - 仅在较大屏幕的横屏模式下应用（从 style.css 提取） */
@media (min-width: 769px) and (orientation: landscape) {
    .container {
        padding: 3px;
    }
    
    header {
        padding: 5px;
    }
    
    header h1 {
        font-size: 1.1em;
        margin-bottom: 2px;
    }
    
    header p {
        font-size: 11px;
    }
    
    .formula-box {
        padding: 6px 8px;
    }
}

/* 桌面端中等屏幕优化（从 style.css 提取） */
@media (max-width: 1200px) and (min-width: 770px) {
    /* 中等屏幕优化：确保词条表格占主要部分 */
    .content-wrapper {
        gap: 12px;
    }
    
    .left-panel {
        flex: 2.5; /* 增加左侧面板比重 */
        min-width: 0;
    }
    
    .right-panel {
        flex: 1; /* 改为flex布局，减少右侧面板比重 */
        width: auto;
        max-width: 280px; /* 限制最大宽度 */
        min-width: 240px; /* 设置最小宽度 */
    }
    
    /* 优化模板区域内容 */
    .template-section {
        padding: 12px;
    }
    
    .template-section h3 {
        font-size: 1em;
        margin-bottom: 10px;
    }
    
    .template-selector {
        margin-bottom: 10px;
    }
    
    .template-selector select {
        padding: 6px 10px;
        font-size: 13px;
    }
    
    .tab-btn {
        padding: 6px 8px;
        font-size: 11px;
    }
    
    .template-content {
        margin-bottom: 10px;
        min-height: 180px; /* 中等屏幕的最小高度 */
        max-height: calc(100vh - 420px); /* 调整最大高度适应中等屏幕 */
        min-width: 220px; /* 设置最小宽度 */
    }
    
    .prop-item {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .apply-btn, .clear-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}