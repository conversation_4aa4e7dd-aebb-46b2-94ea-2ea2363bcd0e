<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="专业的鸣潮游戏词条分数计算器，支持角色模板、OCR识别、权重计算等功能，帮助玩家快速评估声骸词条价值">
    <meta name="keywords" content="鸣潮,词条计算器,声骸,wwuid,游戏工具">
    <title>wwuid词条分数计算器</title>
    <link rel="icon" type="image/webp" href="tt.webp">
    <link rel="stylesheet" href="desktop.css" media="(min-width: 769px)">
    <link rel="stylesheet" href="mobile.css" media="(max-width: 768px)">
    <noscript><link rel="stylesheet" href="style.css"></noscript>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <div class="header-main">
                    <h1><img src="tt.webp" alt="wwuid图标" style="width: 50px; height: 50px; vertical-align: middle;"> wwuid词条分数计算器</h1>
                    <p>根据词条数值和权重计算分数</p>
                </div>
                <div class="header-actions">
                    <button class="ocr-btn" id="ocrBtn">
                        <span class="ocr-icon">📷</span>
                        <span class="ocr-text">OCR识别</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 计算公式说明 -->
        <div class="formula-section">
            <div class="formula-box">
                <div class="formula-header">
                    <h2>📐 计算公式</h2>
                    <div class="formula">词条得分 = (词条数值 × 当前词条权重 ÷ 未对齐最高分) × 对齐分数(50)</div>
                </div>
                <div class="formula-controls">
                    <div class="score-settings">
                        <div class="score-setting-item">
                            <label for="maxScoreMain">未对齐最高分：</label>
                            <input type="number" id="maxScoreMain" value="100" min="1" step="0.1" onchange="updateMaxScore()">
                            <span class="unit">分</span>
                        </div>
                        <div class="score-setting-item">
                            <label for="alignScoreMain">对齐分数：</label>
                            <input type="number" id="alignScoreMain" value="50" min="1" step="0.1" onchange="updateAlignmentScore()">
                            <span class="unit">分</span>
                        </div>
                    </div>
                    <!-- 鸣谢信息放入公式框 -->
                    <div class="formula-acknowledgment">
                        <p>🙏鸣谢 计算公式、权重等信息均来自 <a href="https://github.com/tyql688/WutheringWavesUID" target="_blank" rel="noopener noreferrer">WutheringWavesUID (wwuid)</a></p>
                    </div>
                </div>
            </div>
        </div>

        <main class="main-content">
            <!-- 移动端侧边栏 -->
            <div class="mobile-sidebar" id="mobileSidebar">
                <div class="sidebar-header">
                    <h3><img src="tb.webp" alt="角色模板" style="width: 24px; height: 24px; vertical-align: middle; margin-right: 8px;"> 角色模板</h3>
                    <button class="sidebar-close-btn" id="sidebarCloseBtn">×</button>
                </div>
                <div class="sidebar-content">
                    <div class="template-selector">
                        <label for="mobileCharacterSelect">选择角色：</label>
                        <select id="mobileCharacterSelect" aria-label="选择角色模板">
                            <option value="">请选择角色</option>
                        </select>
                    </div>
                    
                    <div class="template-tabs">
                        <button class="tab-btn active" data-tab="c4">4费位</button>
                        <button class="tab-btn" data-tab="c3">3费位</button>
                        <button class="tab-btn" data-tab="c1">1费位</button>
                    </div>

                    <div class="template-content" id="mobileTemplateContent">
                        <p>请选择角色</p>
                    </div>

                    <div class="template-actions">
                        <button id="mobileApplyTemplate" class="apply-btn">应用模板</button>
                        <button id="mobileClearTemplate" class="clear-btn">清除选择</button>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏遮罩 -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>
            
            <div class="content-wrapper">
                <div class="left-panel">
                    <!-- 移动端角色模板入口 -->
                    <div class="mobile-template-entry">
                        <div class="template-entry-card">
                            <div class="template-entry-icon">
                                <img src="tb.webp" alt="角色模板" style="width: 32px; height: 32px;">
                            </div>
                            <div class="template-entry-content">
                                <h4>角色模板</h4>
                                <p>快速应用角色推荐配置</p>
                            </div>
                            <button class="template-entry-btn" id="templateEntryBtn">
                                <span>选择角色</span>
                                <span class="arrow">→</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-section">
                        <table class="entries-table">
                            <thead>
                                <tr>
                                    <th>词条位置</th>
                                    <th>属性类型</th>
                                    <th>数值</th>
                                    <th>权重</th>
                                    <th>分数</th>
                                </tr>
                            </thead>
                            <tbody id="entriesTableBody">
                                <!-- 表格内容将由JavaScript生成 -->
                            </tbody>
                        </table>
                    </div>

                    <div class="total-score">
                        <h3>总分数: <span id="totalScore">0.00</span></h3>
                    </div>
                </div>

                <div class="right-panel">
                    <div class="template-section">
                        <h3><img src="tb.webp" alt="角色模板" style="width: 24px; height: 24px; vertical-align: middle; margin-right: 8px;"> 角色模板</h3>
                        <div class="template-selector">
                            <label for="characterSelect">选择角色：</label>
                            <select id="characterSelect" aria-label="选择角色模板">
                                <option value="">请选择角色</option>
                            </select>
                        </div>
                        
                        <div class="template-tabs">
                            <button class="tab-btn active" data-tab="c4">4费位</button>
                            <button class="tab-btn" data-tab="c3">3费位</button>
                            <button class="tab-btn" data-tab="c1">1费位</button>
                        </div>

                        <div class="template-content" id="templateContent">
                            <p>请选择角色</p>
                        </div>

                        <div class="template-actions">
                            <button id="applyTemplate" class="apply-btn">应用模板</button>
                            <button id="clearTemplate" class="clear-btn">清除选择</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 页脚区域 -->
        <footer class="footer">
            <div class="footer-content">
                <p>wwuid词条分数计算器 - 鸣谢 <a href="https://github.com/tyql688/WutheringWavesUID" target="_blank" rel="noopener noreferrer">WutheringWavesUID</a></p>
                <p><a href="https://beian.miit.gov.cn/" target="_blank">滇ICP备2025067725号-1</a></p>
            </div>
        </footer>
    </div>

    <!-- OCR弹窗 -->
    <div class="ocr-modal" id="ocrModal">
        <div class="ocr-modal-content">
            <div class="ocr-modal-header">
                <h3>📷 OCR图片识别</h3>
                <button class="ocr-modal-close" id="closeOcrModal">×</button>
            </div>
            <div class="ocr-modal-tabs">
                <button class="ocr-tab-btn active" data-tab="upload">📷 图片识别</button>
                <button class="ocr-tab-btn" data-tab="history">📋 历史记录</button>
            </div>
            <div class="ocr-modal-body">
                <!-- 图片识别标签页 -->
                <div class="ocr-tab-content active" id="uploadTab">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <p class="upload-text">拖拽图片到此处或点击上传</p>
                        <p class="upload-hint">支持 JPG、PNG、GIF 格式</p>
                        <button class="upload-btn" id="uploadBtn">选择图片</button>
                    </div>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>
                <div class="preview-area" id="previewArea" style="display: none;">
                    <div class="preview-header">
                        <h4>预览图片</h4>
                        <button class="preview-remove" id="previewRemove">删除</button>
                    </div>
                    <div class="preview-image">
                        <img id="previewImg" alt="预览图片">
                    </div>
                    <div class="ocr-actions">
                        <button class="ocr-process-btn" id="startOcrBtn">
                            <span class="process-icon">🔍</span>
                            <span class="process-text">开始识别</span>
                        </button>
                    </div>
                </div>
                <div class="ocr-result" id="ocrResult" style="display: none;">
                    <div class="result-header">
                        <h4>识别结果</h4>
                        <button class="result-apply" id="applyResultsBtn">应用到表格</button>
                    </div>
                    <div class="result-content" id="resultContent">
                        <!-- 识别结果将在这里显示 -->
                    </div>
                </div>
                <div class="ocr-loading" id="ocrLoading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>正在识别中，请稍候...</p>
                </div>
                </div>
                
                <!-- 历史记录标签页 -->
                <div class="ocr-tab-content" id="historyTab">
                    <div class="history-header">
                        <h4>📋 OCR历史记录</h4>
                        <div class="history-actions">
                            <button class="history-clear-btn" id="clearHistoryBtn">清空历史</button>
                        </div>
                    </div>
                    <div class="history-content" id="historyContent">
                        <!-- 历史记录内容将由JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>