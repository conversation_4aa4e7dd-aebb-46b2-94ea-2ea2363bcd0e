/* 响应式页脚（从 style.css 提取） */
@media (max-width: 768px) {
    .footer {
        margin: 10px 15px 20px 15px;
        border-radius: 10px;
    }
    
    .footer-content p {
        font-size: 12px;
    }
}
/* 移动端显示菜单按钮，隐藏桌面端角色模板（从 style.css 提取） */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex;
    }
    
    .right-panel {
        display: none !important;
    }
    
    .header-content {
        justify-content: flex-start;
    }
    
    .header-content h1 {
        font-size: 1.3em;
    }
    
    .header-content p {
        font-size: 0.9em;
    }
}
/* 移动端OCR样式调整（从 style.css 提取） */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .ocr-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .ocr-icon {
        font-size: 14px;
    }
    
    .ocr-text {
        display: none;
    }
    
    .ocr-modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .upload-icon {
        font-size: 36px;
    }
    
    .upload-text {
        font-size: 14px;
    }
    
    .upload-hint {
        font-size: 12px;
    }
}

/* 移动端主要布局和表格优化（从 style.css 提取） */
@media (max-width: 769px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel,
    .right-panel {
        width: 100%;
        margin-bottom: 0;
    }
}

/* 移动端全面优化（从 style.css 提取） */
@media (max-width: 768px) {
    body {
        overflow-y: auto;
        font-size: 14px;
    }
    
    .container {
        height: auto;
        min-height: 100vh;
        padding: 5px;
        overflow-x: hidden;
    }
    
    header {
        padding: 10px;
        text-align: center;
    }
    
    header h1 {
        font-size: 1.3em;
        line-height: 1.4;
        margin-bottom: 5px;
    }
    
    header h1 img {
        width: 35px !important;
        height: 35px !important;
    }
    
    header p {
        font-size: 12px;
        margin: 0;
    }
    
    /* 公式区域移动端优化 */
    .formula-section {
        margin-bottom: 8px;
    }
    
    .formula-box {
        padding: 8px 10px;
    }
    
    .formula-header {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
    
    .formula-header h3 {
        font-size: 14px;
        text-align: center;
    }
    
    .formula {
        font-size: 11px;
        padding: 6px 8px;
        min-width: auto;
        word-break: break-all;
        line-height: 1.3;
    }
    
    .formula-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .score-settings {
        justify-content: center;
        gap: 10px;
    }
    
    .score-setting-item {
        flex-direction: column;
        align-items: center;
        gap: 3px;
    }
    
    .score-setting-item label {
        font-size: 11px;
        text-align: center;
    }
    
    .score-setting-item input {
        width: 50px;
        padding: 3px 4px;
        font-size: 11px;
    }
    
    .formula-acknowledgment {
        padding: 5px 8px;
    }
    
    .formula-acknowledgment p {
        font-size: 10px;
        line-height: 1.2;
    }
    
    /* 主内容区域移动端优化 */
    .main-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .left-panel,
    .right-panel {
        width: 100%;
        margin-bottom: 0;
    }
    
    /* 显示移动端角色模板入口 */
    .mobile-template-entry {
        display: block !important;
    }
    
    /* 表格区域移动端优化 */
    .table-section {
        padding: 8px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        max-height: 400px; /* 移动端设置较小的最大高度 */
    }
    
    .entries-table {
        min-width: 500px;
        font-size: 11px;
    }
    
    .entries-table tbody {
        max-height: 300px; /* 移动端表格主体高度 */
    }
    
    /* 移动端滚动条优化 */
    .entries-table tbody::-webkit-scrollbar {
        width: 6px;
    }
}

/* 移动端480px小屏幕优化（从 style.css 提取） */
@media (max-width: 480px) {
    .container {
        padding: 3px;
    }
    
    header {
        padding: 8px;
    }
    
    header h1 {
        font-size: 1.1em;
    }
    
    header h1 img {
        width: 30px !important;
        height: 30px !important;
    }
    
    .formula-box {
        padding: 6px 8px;
    }
    
    .formula {
        font-size: 10px;
        padding: 5px 6px;
    }
    
    .score-settings {
        flex-direction: column;
        align-items: center;
        gap: 6px;
    }
    
    .score-setting-item {
        width: 100%;
        max-width: 150px;
    }
    
    .table-section {
        padding: 6px;
    }
    
    .entries-table {
        min-width: 450px;
    }
    
    .entries-table th,
    .entries-table td {
        padding: 3px 4px;
        font-size: 9px;
    }
    
    .template-section {
        padding: 6px;
    }
    
    .template-content {
        font-size: 10px;
    }
}

/* 水平滚动提示和移动端滚动条（从 style.css 提取） */
@media (max-width: 768px) {
    .table-section {
        position: relative;
    }
    
    .table-section::after {
        display: block;
    }
    
    .table-section::-webkit-scrollbar {
        height: 4px;
    }
    
    .table-section::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }
    
    .table-section::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 2px;
    }
}

/* 移动端触摸优化（从 style.css 提取） */
@media (max-width: 768px) {
    /* 增大触摸目标 */
    .table-select,
    .table-input,
    .tab-btn,
    .apply-btn,
    .clear-btn,
    button,
    select {
        min-height: 32px;
        touch-action: manipulation;
    }
    
    /* 触摸反馈 */
    .tab-btn:active,
    .apply-btn:active,
    .clear-btn:active,
    button:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    /* 防止双击缩放 */
    input,
    select,
    button {
        touch-action: manipulation;
    }
    
    /* 优化选择框在移动端的显示 */
    .template-selector select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 8px center;
        background-size: 16px;
        padding-right: 32px;
    }
    
    /* 表格滚动区域优化 */
    .table-section {
        border-radius: 8px;
        box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
    }
    
    /* 滚动指示器 */
    .table-section::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 20px;
        background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
        pointer-events: none;
        z-index: 1;
    }
}

/* 超小屏幕360px优化（从 style.css 提取） */
@media (max-width: 360px) {
    .container {
        padding: 2px;
    }
    
    header h1 {
        font-size: 1em;
        word-break: break-word;
    }
    
    .formula {
        font-size: 9px;
        padding: 4px 5px;
    }
    
    .entries-table {
        min-width: 400px;
    }
    
    .entries-table th,
    .entries-table td {
        padding: 2px 3px;
        font-size: 8px;
    }
    
    .score-setting-item input {
        width: 45px;
        padding: 2px 3px;
        font-size: 10px;
    }
}

/* 移动端强制垂直布局（从 style.css 提取） */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column !important;
        gap: 10px !important;
    }
    
    .left-panel,
    .right-panel {
        width: 100% !important;
        flex: none !important;
        min-width: auto !important;
        margin-bottom: 0 !important;
    }
    
    /* 确保角色模板区域显示 */
    .right-panel {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        order: 2; /* 确保在表格后面显示 */
    }
    
    .template-section {
        display: block !important;
        visibility: visible !important;
        height: auto !important;
        min-height: 200px;
    }
    
    .template-content {
        max-height: 300px !important;
        overflow-y: auto;
    }
}

/* 移动端样式增强（从 style.css 提取） */
@media (max-width: 768px) {
    /* 优化焦点状态 */
    .table-input:focus,
    .table-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        outline: none;
    }
    
    /* 优化按钮状态 */
    .tab-btn.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }
    
    /* 表格行悬停效果（仅在非触摸设备上） */
    @media (hover: hover) {
        .entries-table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }
    }
    
    /* 滚动条在移动端的优化 */
    .table-section::-webkit-scrollbar {
        height: 6px;
    }
    
    .template-content::-webkit-scrollbar {
        width: 4px;
    }
    
    .template-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }
    
    .template-content::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 2px;
    }
}