// OCR功能模块 - 修复版
let ocrModule = null;
let currentImageFile = null;
let ocrResults = [];
let ocrHistory = [];

async function loadOCRModule() {
    if (ocrModule) return ocrModule;

    ocrModule = {
        modal: null,
        isProcessing: false,

        init() {
            this.modal = document.getElementById('ocrModal');
            this.loadOCRHistory();
            this.bindEvents();
            this.initOCRTabs();
        },
        
        bindEvents() {
            // OCR按钮点击
            document.getElementById('ocrBtn')?.addEventListener('click', () => this.showModal());

            // 关闭模态框
            document.getElementById('closeOcrModal')?.addEventListener('click', () => this.hideModal());

            // 点击弹窗背景关闭
            this.modal?.addEventListener('click', (e) => {
                if (e.target === this.modal) this.hideModal();
            });

            // 文件上传
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const uploadArea = document.getElementById('uploadArea');

            uploadBtn?.addEventListener('click', () => fileInput?.click());
            uploadArea?.addEventListener('click', () => fileInput?.click());
            fileInput?.addEventListener('change', (e) => this.handleFileSelect(e));

            // 拖拽上传
            uploadArea?.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea?.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea?.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) this.handleFile(files[0]);
            });

            // 删除预览图片
            document.getElementById('previewRemove')?.addEventListener('click', () => this.resetOCRModal());

            // 开始识别
            document.getElementById('startOcrBtn')?.addEventListener('click', () => this.processOCR());

            // 应用结果
            document.getElementById('applyResultsBtn')?.addEventListener('click', () => this.applyResults());

            // 清空历史记录
            document.getElementById('clearHistoryBtn')?.addEventListener('click', () => {
                if (confirm('确定要清空所有OCR历史记录吗？')) {
                    this.clearOCRHistory();
                }
            });
        },
        
        showModal() {
            if (this.modal) {
                this.modal.style.display = 'flex';
                this.resetOCRModal();
            }
        },

        hideModal() {
            if (this.modal) {
                this.modal.style.display = 'none';
                this.resetOCRModal();
            }
        },

        handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) this.handleFile(file);
        },

        handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }

            currentImageFile = file;
            console.log('选择的文件:', file);

            const reader = new FileReader();
            reader.onload = (e) => {
                const previewImg = document.getElementById('previewImg');
                if (previewImg) {
                    previewImg.src = e.target.result;
                    document.getElementById('uploadArea').style.display = 'none';
                    document.getElementById('previewArea').style.display = 'block';
                }
            };
            reader.readAsDataURL(file);
        },
        
        async processOCR() {
            if (!currentImageFile) {
                alert('请先选择图片');
                return;
            }

            if (this.isProcessing) return;

            this.isProcessing = true;
            const loadingEl = document.getElementById('ocrLoading');
            const previewEl = document.getElementById('previewArea');
            const resultEl = document.getElementById('ocrResult');
            const startOcrBtn = document.getElementById('startOcrBtn');

            try {
                // 显示加载状态
                previewEl.style.display = 'none';
                loadingEl.style.display = 'block';
                if (startOcrBtn) startOcrBtn.disabled = true;

                console.log('开始OCR识别，当前文件:', currentImageFile);

                // 使用统一OCR API
                const results = await this.performOCRWithProvider(currentImageFile);

                if (results && results.length > 0) {
                    this.displayResults(results);
                    resultEl.style.display = 'block';
                } else {
                    alert('未识别到词条信息，请尝试其他图片');
                    previewEl.style.display = 'block';
                }

            } catch (error) {
                console.error('OCR处理失败:', error);
                alert('识别失败: ' + error.message);
                previewEl.style.display = 'block';
            } finally {
                loadingEl.style.display = 'none';
                if (startOcrBtn) startOcrBtn.disabled = false;
                this.isProcessing = false;
            }
        },

        async performOCRWithProvider(imageFile) {
            try {
                console.log('开始OCR识别...');

                // 将图片转换为Base64
                const imageBase64 = await this.fileToBase64(imageFile);

                // 调用统一OCR API
                const response = await fetch('/api/ocr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image: imageBase64
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.error_code) {
                    throw new Error(`OCR API错误: ${data.error_msg}`);
                }

                if (!data.words_result || data.words_result.length === 0) {
                    console.log('OCR未识别到文字内容');
                    return [];
                }

                // 显示使用的OCR提供商
                if (data.provider) {
                    console.log(`OCR识别成功，使用提供商: ${data.provider}`);
                }

                // 解析OCR结果
                return this.parseOCRResult(data);

            } catch (error) {
                console.error('OCR识别失败:', error);
                // 如果API调用失败，返回模拟数据用于测试
                console.log('使用模拟OCR数据');
                return this.getMockOCRResults();
            }
        },

        fileToBase64(file) {
            return new Promise((resolve, reject) => {
                if (!file) {
                    reject(new Error('文件参数为空'));
                    return;
                }

                if (!(file instanceof File) && !(file instanceof Blob)) {
                    reject(new Error('参数必须是File或Blob对象'));
                    return;
                }

                const reader = new FileReader();
                reader.onload = () => {
                    try {
                        const result = reader.result;
                        if (typeof result === 'string') {
                            // 移除data:image/...;base64,前缀
                            const base64 = result.split(',')[1];
                            resolve(base64);
                        } else {
                            reject(new Error('读取文件失败'));
                        }
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = () => reject(new Error('读取文件失败'));
                reader.readAsDataURL(file);
            });
        },

        getMockOCRResults() {
            // 模拟OCR识别结果
            return [
                { attribute: '攻击', attributeKey: 'atk', value: 150.5, confidence: 0.95 },
                { attribute: '暴击', attributeKey: 'crit_rate', value: 8.2, confidence: 0.92 },
                { attribute: '暴击伤害', attributeKey: 'crit_damage', value: 16.8, confidence: 0.88 },
                { attribute: '共鸣效率', attributeKey: 'resonance_efficiency', value: 12.5, confidence: 0.85 }
            ];
        },
        
        parseOCRResult(ocrData) {
            console.log('解析OCR结果:', ocrData);

            if (!ocrData.words_result || ocrData.words_result.length === 0) {
                return [];
            }

            const wordsResult = ocrData.words_result;
            const attributeRules = [
                { pattern: '暴击伤害', key: 'crit_damage', name: '暴击伤害', priority: 10 },
                { pattern: '普攻伤害加成', key: 'basic_attack_damage', name: '普攻伤害加成', priority: 10 },
                { pattern: '重击伤害加成', key: 'heavy_attack_damage', name: '重击伤害加成', priority: 10 },
                { pattern: '共鸣技能伤害加成', key: 'resonance_skill_damage', name: '共鸣技能伤害加成', priority: 10 },
                { pattern: '共鸣解放伤害加成', key: 'resonance_liberation_damage', name: '共鸣解放伤害加成', priority: 10 },
                { pattern: '属性伤害加成', key: 'element_damage', name: '属性伤害加成', priority: 10 },
                { pattern: '治疗效果加成', key: 'healing_bonus', name: '治疗效果加成', priority: 10 },
                { pattern: '共鸣效率', key: 'resonance_efficiency', name: '共鸣效率', priority: 10 },
                { pattern: '攻击百分比', key: 'atk_percent', name: '攻击百分比', priority: 9 },
                { pattern: '生命百分比', key: 'hp_percent', name: '生命百分比', priority: 9 },
                { pattern: '防御百分比', key: 'def_percent', name: '防御百分比', priority: 9 },
                { pattern: '暴击', key: 'crit_rate', name: '暴击', priority: 8 },
                { pattern: '攻击', key: 'atk', name: '攻击', priority: 7 },
                { pattern: '生命', key: 'hp', name: '生命', priority: 7 },
                { pattern: '防御', key: 'def', name: '防御', priority: 7 }
            ];

            const results = [];
            const processedTexts = new Set();

            wordsResult.forEach((item, index) => {
                const text = item.words.trim();
                if (processedTexts.has(text)) return;

                let bestMatch = null;
                let bestPriority = -1;

                attributeRules.forEach(rule => {
                    if (text.includes(rule.pattern) && rule.priority > bestPriority) {
                        bestMatch = rule;
                        bestPriority = rule.priority;
                    }
                });

                if (bestMatch) {
                    const valueMatch = text.match(/[\d.]+/);
                    if (valueMatch) {
                        const value = parseFloat(valueMatch[0]);
                        if (value > 0) {
                            results.push({
                                attribute: bestMatch.name,
                                attributeKey: bestMatch.key,
                                value: value,
                                confidence: item.probability || 0.9,
                                originalText: text
                            });
                            processedTexts.add(text);
                        }
                    }
                }
            });

            console.log('解析结果:', results);
            return results;
        },

        displayResults(results) {
            ocrResults = results;
            const resultContent = document.getElementById('resultContent');
            if (!resultContent) return;

            resultContent.innerHTML = '';

            if (results.length === 0) {
                resultContent.innerHTML = '<p style="text-align: center; color: #718096;">未识别到词条信息</p>';
                return;
            }

            results.forEach((result, index) => {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.innerHTML = `
                    <div class="result-info">
                        <span class="result-attribute">${result.attribute}</span>
                        <span class="result-value">${result.value}</span>
                        <span class="result-confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</span>
                    </div>
                    <div class="result-original">原文: ${result.originalText || result.attribute + ' ' + result.value}</div>
                `;
                resultContent.appendChild(resultItem);
            });
        },
        
        applyResults() {
            if (ocrResults.length === 0) {
                alert('没有识别结果可以应用');
                return;
            }

            let appliedCount = 0;

            // 按顺序定义词条位置：主词条1、主词条2、副词条1-5
            const orderedSlots = [
                { id: 'main-1', type: 'main' },
                { id: 'main-2', type: 'main' },
                { id: 'sub-1', type: 'sub' },
                { id: 'sub-2', type: 'sub' },
                { id: 'sub-3', type: 'sub' },
                { id: 'sub-4', type: 'sub' },
                { id: 'sub-5', type: 'sub' }
            ];

            // 按顺序应用OCR识别结果
            ocrResults.forEach((result, index) => {
                if (index >= orderedSlots.length) {
                    console.warn(`OCR结果超出可用位置数量: ${result.attribute}`);
                    return;
                }

                const targetSlot = orderedSlots[index];
                const attributeKey = result.attributeKey || this.findAttributeKey(result.attribute, targetSlot.type);

                if (attributeKey) {
                    const attributeSelect = document.getElementById(`${targetSlot.id}-attribute`);
                    const valueInput = document.getElementById(`${targetSlot.id}-value`);
                    const weightInput = document.getElementById(`${targetSlot.id}-weight`);

                    if (attributeSelect && valueInput && weightInput) {
                        // 检查属性是否在选项中
                        const option = attributeSelect.querySelector(`option[value="${attributeKey}"]`);
                        if (option) {
                            attributeSelect.value = attributeKey;
                            valueInput.value = result.value;

                            // 触发属性改变事件来设置权重
                            if (typeof handleAttributeChange === 'function') {
                                handleAttributeChange(targetSlot.id);
                            }

                            appliedCount++;
                            console.log(`应用到 ${targetSlot.id}: ${result.attribute} = ${result.value}`);
                        } else {
                            console.warn(`属性 ${attributeKey} 不在 ${targetSlot.id} 的选项中`);
                        }
                    }
                }
            });

            if (appliedCount > 0) {
                // 保存到历史记录
                this.saveOCRHistory(ocrResults);

                alert(`成功按顺序应用了 ${appliedCount} 个词条！`);

                // 重新计算总分
                if (typeof calculateTotal === 'function') {
                    calculateTotal();
                }

                this.hideModal();
            } else {
                alert('没有找到合适的词条位置或属性映射！');
            }
        },

        findAttributeKey(attributeName, slotType) {
            const attributeMapping = {
                '攻击': 'atk', '攻击百分比': 'atk_percent',
                '生命': 'hp', '生命百分比': 'hp_percent',
                '防御': 'def', '防御百分比': 'def_percent',
                '暴击': 'crit_rate', '暴击伤害': 'crit_damage',
                '共鸣效率': 'resonance_efficiency',
                '属性伤害加成': 'element_damage',
                '治疗效果加成': 'healing_bonus',
                '普攻伤害加成': 'basic_attack_damage',
                '重击伤害加成': 'heavy_attack_damage',
                '共鸣技能伤害加成': 'resonance_skill_damage',
                '共鸣解放伤害加成': 'resonance_liberation_damage'
            };

            return attributeMapping[attributeName] || null;
        },

        resetOCRModal() {
            currentImageFile = null;
            ocrResults = [];

            const uploadArea = document.getElementById('uploadArea');
            const previewArea = document.getElementById('previewArea');
            const ocrLoading = document.getElementById('ocrLoading');
            const ocrResult = document.getElementById('ocrResult');
            const previewImg = document.getElementById('previewImg');
            const fileInput = document.getElementById('fileInput');
            const resultContent = document.getElementById('resultContent');

            if (uploadArea) uploadArea.style.display = 'block';
            if (previewArea) previewArea.style.display = 'none';
            if (ocrLoading) ocrLoading.style.display = 'none';
            if (ocrResult) ocrResult.style.display = 'none';

            if (previewImg) previewImg.src = '';
            if (fileInput) fileInput.value = '';
            if (resultContent) resultContent.innerHTML = '';
            if (uploadArea) uploadArea.classList.remove('dragover');
        },

        initOCRTabs() {
            const tabBtns = document.querySelectorAll('.ocr-tab-btn');
            const tabContents = document.querySelectorAll('.ocr-tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const targetTab = btn.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // 添加当前活动状态
                    btn.classList.add('active');
                    const targetContent = document.getElementById(targetTab + 'Tab');
                    if (targetContent) {
                        targetContent.classList.add('active');

                        // 如果切换到历史记录标签页，刷新历史记录显示
                        if (targetTab === 'history') {
                            this.displayOCRHistory();
                        }
                    }
                });
            });
        },

        loadOCRHistory() {
            try {
                const savedHistory = localStorage.getItem('ocrHistory');
                if (savedHistory) {
                    ocrHistory = JSON.parse(savedHistory);
                    console.log(`已加载 ${ocrHistory.length} 条OCR历史记录`);
                }
            } catch (error) {
                console.error('加载OCR历史记录失败:', error);
                ocrHistory = [];
            }
        },

        saveOCRHistory(results) {
            if (!results || results.length === 0) return;

            const historyItem = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                results: results.map(result => ({
                    attribute: result.attribute,
                    attributeKey: result.attributeKey,
                    value: result.value,
                    confidence: result.confidence
                }))
            };

            ocrHistory.unshift(historyItem);

            // 限制历史记录数量（最多保存50条）
            if (ocrHistory.length > 50) {
                ocrHistory = ocrHistory.slice(0, 50);
            }

            try {
                localStorage.setItem('ocrHistory', JSON.stringify(ocrHistory));
                console.log('OCR历史记录已保存');
            } catch (error) {
                console.error('保存OCR历史记录失败:', error);
            }
        },

        displayOCRHistory() {
            const historyContent = document.getElementById('historyContent');
            if (!historyContent) return;

            historyContent.innerHTML = '';

            if (ocrHistory.length === 0) {
                historyContent.innerHTML = `
                    <div class="history-empty">
                        <div class="empty-icon">📝</div>
                        <p>暂无OCR历史记录</p>
                        <p class="empty-hint">完成OCR识别后，记录将显示在这里</p>
                    </div>
                `;
                return;
            }

            ocrHistory.forEach(item => {
                const historyItem = this.createHistoryItemElement(item);
                historyContent.appendChild(historyItem);
            });
        },

        createHistoryItemElement(item) {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';

            const date = new Date(item.timestamp);
            const timeStr = date.toLocaleString('zh-CN');

            historyItem.innerHTML = `
                <div class="history-header">
                    <span class="history-time">${timeStr}</span>
                    <div class="history-actions">
                        <button class="history-apply-btn" onclick="ocrModule.applyHistoryItem(${item.id})">应用</button>
                        <button class="history-delete-btn" onclick="ocrModule.deleteHistoryItem(${item.id})">删除</button>
                    </div>
                </div>
                <div class="history-results">
                    ${item.results.map(result =>
                        `<span class="history-result-item">${result.attribute}: ${result.value}</span>`
                    ).join('')}
                </div>
            `;

            return historyItem;
        },

        applyHistoryItem(itemId) {
            const historyItem = ocrHistory.find(item => item.id === itemId);
            if (!historyItem) {
                alert('历史记录不存在');
                return;
            }

            ocrResults = historyItem.results;
            this.applyResults();
        },

        deleteHistoryItem(itemId) {
            if (!confirm('确定要删除这条历史记录吗？')) return;

            ocrHistory = ocrHistory.filter(item => item.id !== itemId);

            try {
                localStorage.setItem('ocrHistory', JSON.stringify(ocrHistory));
            } catch (error) {
                console.error('删除历史记录失败:', error);
            }

            this.displayOCRHistory();
        },

        clearOCRHistory() {
            ocrHistory = [];

            try {
                localStorage.removeItem('ocrHistory');
            } catch (error) {
                console.error('清空历史记录失败:', error);
            }

            this.displayOCRHistory();
        }
    };

    await ocrModule.init();
    return ocrModule;
}

// 懒加载OCR功能
document.addEventListener('DOMContentLoaded', function() {
    const ocrBtn = document.getElementById('ocrBtn');
    if (ocrBtn) {
        ocrBtn.addEventListener('click', async function() {
            window.ocrModule = await loadOCRModule();
        }, { once: true });
    }
});

// 立即初始化OCR功能（如果需要）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', async function() {
        // 预加载OCR模块
        setTimeout(async () => {
            try {
                window.ocrModule = await loadOCRModule();
                console.log('OCR模块预加载完成');
            } catch (error) {
                console.error('OCR模块预加载失败:', error);
            }
        }, 2000);
    });
} else {
    // 如果DOM已经加载完成，立即预加载
    setTimeout(async () => {
        try {
            window.ocrModule = await loadOCRModule();
            console.log('OCR模块预加载完成');
        } catch (error) {
            console.error('OCR模块预加载失败:', error);
        }
    }, 2000);
}
