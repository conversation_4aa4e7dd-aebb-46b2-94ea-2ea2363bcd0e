// OCR功能模块 - 懒加载
let ocrModule = null;

async function loadOCRModule() {
    if (ocrModule) return ocrModule;
    
    ocrModule = {
        modal: null,
        isProcessing: false,
        
        init() {
            this.modal = document.getElementById('ocrModal');
            this.bindEvents();
        },
        
        bindEvents() {
            // OCR按钮点击
            document.getElementById('ocrBtn')?.addEventListener('click', () => this.showModal());
            
            // 关闭模态框
            document.getElementById('closeOcrModal')?.addEventListener('click', () => this.hideModal());
            
            // 文件上传
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const uploadArea = document.getElementById('uploadArea');
            
            uploadBtn?.addEventListener('click', () => fileInput?.click());
            fileInput?.addEventListener('change', (e) => this.handleFileSelect(e));
            
            // 拖拽上传
            uploadArea?.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });
            
            uploadArea?.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });
            
            uploadArea?.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                const files = e.dataTransfer.files;
                if (files.length > 0) this.handleFile(files[0]);
            });
            
            // 开始识别
            document.getElementById('startOcrBtn')?.addEventListener('click', () => this.processOCR());
            
            // 应用结果
            document.getElementById('applyResultsBtn')?.addEventListener('click', () => this.applyResults());
        },
        
        showModal() {
            this.modal?.classList.add('show');
        },
        
        hideModal() {
            this.modal?.classList.remove('show');
        },
        
        handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) this.handleFile(file);
        },
        
        handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewImg = document.getElementById('previewImg');
                if (previewImg) {
                    previewImg.src = e.target.result;
                    document.getElementById('uploadArea').style.display = 'none';
                    document.getElementById('previewArea').style.display = 'block';
                }
            };
            reader.readAsDataURL(file);
        },
        
        async processOCR() {
            if (this.isProcessing) return;
            
            this.isProcessing = true;
            const loadingEl = document.getElementById('ocrLoading');
            const previewEl = document.getElementById('previewArea');
            const resultEl = document.getElementById('ocrResult');
            
            // 显示加载状态
            previewEl.style.display = 'none';
            loadingEl.style.display = 'block';
            
            try {
                // 模拟OCR处理
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 模拟识别结果
                const mockResults = [
                    { type: '攻击力', value: 150.5, position: 'sub-1' },
                    { type: '暴击', value: 8.2, position: 'sub-2' },
                    { type: '暴击伤害', value: 16.8, position: 'sub-3' }
                ];
                
                this.displayResults(mockResults);
                
                // 显示结果
                loadingEl.style.display = 'none';
                resultEl.style.display = 'block';
                
            } catch (error) {
                console.error('OCR处理失败:', error);
                alert('识别失败，请重试');
                loadingEl.style.display = 'none';
                previewEl.style.display = 'block';
            } finally {
                this.isProcessing = false;
            }
        },
        
        displayResults(results) {
            const resultContent = document.getElementById('resultContent');
            if (!resultContent) return;
            
            resultContent.innerHTML = results.map(result => `
                <div class="result-item">
                    <span class="result-type">${result.type}</span>
                    <span class="result-value">${result.value}</span>
                    <span class="result-position">${result.position}</span>
                </div>
            `).join('');
        },
        
        applyResults() {
            // 应用识别结果到表格
            const resultItems = document.querySelectorAll('.result-item');
            resultItems.forEach(item => {
                const type = item.querySelector('.result-type')?.textContent;
                const value = item.querySelector('.result-value')?.textContent;
                const position = item.querySelector('.result-position')?.textContent;
                
                if (type && value && position) {
                    const typeEl = document.getElementById(`${position}-type`);
                    const valueEl = document.getElementById(`${position}-value`);
                    
                    if (typeEl && valueEl) {
                        typeEl.value = type;
                        valueEl.value = value;
                    }
                }
            });
            
            // 重新计算总分
            if (typeof calculateTotal === 'function') {
                calculateTotal();
            }
            
            this.hideModal();
        }
    };
    
    ocrModule.init();
    return ocrModule;
}

// 懒加载OCR功能
document.addEventListener('DOMContentLoaded', function() {
    const ocrBtn = document.getElementById('ocrBtn');
    if (ocrBtn) {
        ocrBtn.addEventListener('click', async function() {
            await loadOCRModule();
        }, { once: true });
    }
});
