// 性能监控脚本
(function() {
    'use strict';
    
    // 性能监控配置
    const PERFORMANCE_CONFIG = {
        enabled: true,
        logToConsole: true,
        sendToServer: false, // 设置为true时发送到服务器
        serverEndpoint: '/api/performance',
        thresholds: {
            fcp: 2000,  // First Contentful Paint
            lcp: 4000,  // Largest Contentful Paint
            fid: 100,   // First Input Delay
            cls: 0.1    // Cumulative Layout Shift
        }
    };
    
    // 性能数据收集器
    const PerformanceMonitor = {
        metrics: {},
        
        init() {
            if (!PERFORMANCE_CONFIG.enabled) return;
            
            this.measureLoadTimes();
            this.measureWebVitals();
            this.measureResourceTimes();
            this.setupErrorTracking();
            
            // 页面卸载时发送数据
            window.addEventListener('beforeunload', () => {
                this.sendMetrics();
            });
        },
        
        measureLoadTimes() {
            window.addEventListener('load', () => {
                const perfData = performance.getEntriesByType('navigation')[0];
                
                this.metrics.loadTimes = {
                    dns: perfData.domainLookupEnd - perfData.domainLookupStart,
                    tcp: perfData.connectEnd - perfData.connectStart,
                    request: perfData.responseStart - perfData.requestStart,
                    response: perfData.responseEnd - perfData.responseStart,
                    dom: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    load: perfData.loadEventEnd - perfData.loadEventStart,
                    total: perfData.loadEventEnd - perfData.fetchStart
                };
                
                this.logMetric('页面加载时间', this.metrics.loadTimes);
            });
        },
        
        measureWebVitals() {
            // First Contentful Paint
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const fcp = entries[entries.length - 1];
                this.metrics.fcp = fcp.startTime;
                this.checkThreshold('FCP', fcp.startTime, PERFORMANCE_CONFIG.thresholds.fcp);
            }).observe({ entryTypes: ['paint'] });
            
            // Largest Contentful Paint
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lcp = entries[entries.length - 1];
                this.metrics.lcp = lcp.startTime;
                this.checkThreshold('LCP', lcp.startTime, PERFORMANCE_CONFIG.thresholds.lcp);
            }).observe({ entryTypes: ['largest-contentful-paint'] });
            
            // First Input Delay
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.name === 'first-input') {
                        const fid = entry.processingStart - entry.startTime;
                        this.metrics.fid = fid;
                        this.checkThreshold('FID', fid, PERFORMANCE_CONFIG.thresholds.fid);
                    }
                });
            }).observe({ entryTypes: ['first-input'] });
            
            // Cumulative Layout Shift
            let clsValue = 0;
            new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                }
                this.metrics.cls = clsValue;
                this.checkThreshold('CLS', clsValue, PERFORMANCE_CONFIG.thresholds.cls);
            }).observe({ entryTypes: ['layout-shift'] });
        },
        
        measureResourceTimes() {
            window.addEventListener('load', () => {
                const resources = performance.getEntriesByType('resource');
                const resourceMetrics = {
                    css: [],
                    js: [],
                    images: [],
                    other: []
                };
                
                resources.forEach(resource => {
                    const duration = resource.responseEnd - resource.startTime;
                    const resourceData = {
                        name: resource.name,
                        duration: duration,
                        size: resource.transferSize || 0
                    };
                    
                    if (resource.name.includes('.css')) {
                        resourceMetrics.css.push(resourceData);
                    } else if (resource.name.includes('.js')) {
                        resourceMetrics.js.push(resourceData);
                    } else if (resource.name.match(/\.(png|jpg|jpeg|gif|webp|svg)$/)) {
                        resourceMetrics.images.push(resourceData);
                    } else {
                        resourceMetrics.other.push(resourceData);
                    }
                });
                
                this.metrics.resources = resourceMetrics;
                this.logMetric('资源加载时间', resourceMetrics);
            });
        },
        
        setupErrorTracking() {
            // JavaScript错误监控
            window.addEventListener('error', (event) => {
                this.trackError({
                    type: 'javascript',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error ? event.error.stack : null
                });
            });
            
            // Promise错误监控
            window.addEventListener('unhandledrejection', (event) => {
                this.trackError({
                    type: 'promise',
                    message: event.reason.message || event.reason,
                    stack: event.reason.stack
                });
            });
            
            // 资源加载错误监控
            window.addEventListener('error', (event) => {
                if (event.target !== window) {
                    this.trackError({
                        type: 'resource',
                        element: event.target.tagName,
                        source: event.target.src || event.target.href,
                        message: '资源加载失败'
                    });
                }
            }, true);
        },
        
        trackError(errorData) {
            if (!this.metrics.errors) {
                this.metrics.errors = [];
            }
            
            this.metrics.errors.push({
                ...errorData,
                timestamp: Date.now(),
                url: window.location.href,
                userAgent: navigator.userAgent
            });
            
            this.logMetric('错误追踪', errorData);
        },
        
        checkThreshold(metric, value, threshold) {
            const status = value <= threshold ? '✅' : '⚠️';
            this.logMetric(`${metric} ${status}`, `${value.toFixed(2)}ms (阈值: ${threshold}ms)`);
        },
        
        logMetric(name, data) {
            if (PERFORMANCE_CONFIG.logToConsole) {
                console.log(`[性能监控] ${name}:`, data);
            }
        },
        
        sendMetrics() {
            if (!PERFORMANCE_CONFIG.sendToServer) return;
            
            const payload = {
                url: window.location.href,
                timestamp: Date.now(),
                userAgent: navigator.userAgent,
                metrics: this.metrics
            };
            
            // 使用sendBeacon发送数据（不阻塞页面卸载）
            if (navigator.sendBeacon) {
                navigator.sendBeacon(
                    PERFORMANCE_CONFIG.serverEndpoint,
                    JSON.stringify(payload)
                );
            } else {
                // 备用方案
                fetch(PERFORMANCE_CONFIG.serverEndpoint, {
                    method: 'POST',
                    body: JSON.stringify(payload),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    keepalive: true
                }).catch(error => {
                    console.error('发送性能数据失败:', error);
                });
            }
        },
        
        // 手动触发性能报告
        generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                metrics: this.metrics,
                summary: this.generateSummary()
            };
            
            console.table(report.summary);
            return report;
        },
        
        generateSummary() {
            const summary = {};
            
            if (this.metrics.loadTimes) {
                summary['总加载时间'] = `${this.metrics.loadTimes.total.toFixed(2)}ms`;
            }
            
            if (this.metrics.fcp) {
                summary['首次内容绘制'] = `${this.metrics.fcp.toFixed(2)}ms`;
            }
            
            if (this.metrics.lcp) {
                summary['最大内容绘制'] = `${this.metrics.lcp.toFixed(2)}ms`;
            }
            
            if (this.metrics.fid) {
                summary['首次输入延迟'] = `${this.metrics.fid.toFixed(2)}ms`;
            }
            
            if (this.metrics.cls) {
                summary['累积布局偏移'] = this.metrics.cls.toFixed(3);
            }
            
            if (this.metrics.errors) {
                summary['错误数量'] = this.metrics.errors.length;
            }
            
            return summary;
        }
    };
    
    // 初始化性能监控
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            PerformanceMonitor.init();
        });
    } else {
        PerformanceMonitor.init();
    }
    
    // 暴露到全局作用域（用于调试）
    window.PerformanceMonitor = PerformanceMonitor;
    
})();
