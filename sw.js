// Service Worker - 缓存策略
const CACHE_NAME = 'wwuid-calculator-v1.0';
const STATIC_CACHE = 'wwuid-static-v1.0';
const DYNAMIC_CACHE = 'wwuid-dynamic-v1.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/core.js',
    '/config.js',
    '/critical.css',
    '/desktop.css',
    '/mobile.css',
    '/tt.webp',
    '/tb.webp',
    '/bg.webp',
    '/ba.png'
];

// 需要网络优先的资源
const NETWORK_FIRST = [
    '/character/',
    '/ocr.js',
    '/templates.js'
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('静态资源缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('缓存静态资源失败:', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker 激活完成');
                return self.clients.claim();
            })
    );
});

// 拦截请求 - 实现缓存策略
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 只处理同源请求
    if (url.origin !== location.origin) {
        return;
    }
    
    // 静态资源 - 缓存优先
    if (STATIC_ASSETS.some(asset => url.pathname.endsWith(asset))) {
        event.respondWith(cacheFirst(request));
        return;
    }
    
    // 动态资源 - 网络优先
    if (NETWORK_FIRST.some(pattern => url.pathname.includes(pattern))) {
        event.respondWith(networkFirst(request));
        return;
    }
    
    // HTML页面 - 网络优先，缓存备用
    if (request.headers.get('accept').includes('text/html')) {
        event.respondWith(networkFirst(request));
        return;
    }
    
    // 其他资源 - 缓存优先
    event.respondWith(cacheFirst(request));
});

// 缓存优先策略
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            console.log('从缓存返回:', request.url);
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
            console.log('网络请求并缓存:', request.url);
        }
        return networkResponse;
    } catch (error) {
        console.error('缓存优先策略失败:', error);
        return new Response('离线状态，资源不可用', { status: 503 });
    }
}

// 网络优先策略
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
            console.log('网络请求成功并缓存:', request.url);
        }
        return networkResponse;
    } catch (error) {
        console.log('网络请求失败，尝试缓存:', request.url);
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        return new Response('资源不可用', { status: 503 });
    }
}

// 清理过期缓存
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        event.waitUntil(
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => caches.delete(cacheName))
                );
            })
        );
    }
});
