// 角色模板模块 - 懒加载
let templatesModule = null;

async function loadTemplatesModule() {
    if (templatesModule) return templatesModule;
    
    templatesModule = {
        characters: [],
        currentCharacter: null,
        currentTab: 'c4',
        
        async init() {
            await this.loadCharacters();
            this.bindEvents();
            this.populateCharacterSelects();
        },
        
        async loadCharacters() {
            try {
                // 从config.js获取角色列表
                if (typeof getCharacterList === 'function') {
                    this.characters = getCharacterList();
                } else if (window.CharacterConfig) {
                    this.characters = window.CharacterConfig.getCharacterList();
                } else {
                    // 备用角色列表
                    this.characters = ['安可', '白芷', '长离', '炽霞', '椿'];
                }
            } catch (error) {
                console.error('加载角色列表失败:', error);
                this.characters = ['安可', '白芷', '长离', '炽霞', '椿'];
            }
        },
        
        bindEvents() {
            // 桌面端角色选择
            const characterSelect = document.getElementById('characterSelect');
            characterSelect?.addEventListener('change', (e) => this.selectCharacter(e.target.value));
            
            // 移动端角色选择
            const mobileCharacterSelect = document.getElementById('mobileCharacterSelect');
            mobileCharacterSelect?.addEventListener('change', (e) => this.selectCharacter(e.target.value));
            
            // 标签页切换
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const tab = btn.getAttribute('data-tab');
                    this.switchTab(tab);
                });
            });
            
            // 应用模板按钮
            document.getElementById('applyTemplate')?.addEventListener('click', () => this.applyTemplate());
            document.getElementById('mobileApplyTemplate')?.addEventListener('click', () => this.applyTemplate());
            
            // 清除模板按钮
            document.getElementById('clearTemplate')?.addEventListener('click', () => this.clearTemplate());
            document.getElementById('mobileClearTemplate')?.addEventListener('click', () => this.clearTemplate());
        },
        
        populateCharacterSelects() {
            const selects = [
                document.getElementById('characterSelect'),
                document.getElementById('mobileCharacterSelect')
            ];
            
            selects.forEach(select => {
                if (select) {
                    select.innerHTML = '<option value="">请选择角色</option>';
                    this.characters.forEach(character => {
                        const option = document.createElement('option');
                        option.value = character;
                        option.textContent = character;
                        select.appendChild(option);
                    });
                }
            });
        },
        
        async selectCharacter(characterName) {
            if (!characterName) {
                this.currentCharacter = null;
                this.updateTemplateContent('请选择角色');
                return;
            }
            
            try {
                const templateData = await this.loadCharacterTemplate(characterName);
                this.currentCharacter = { name: characterName, data: templateData };
                this.updateTemplateDisplay();
            } catch (error) {
                console.error('加载角色模板失败:', error);
                this.updateTemplateContent('加载模板失败');
            }
        },
        
        async loadCharacterTemplate(characterName) {
            try {
                const response = await fetch(`character/${characterName}/calc.json`);
                if (!response.ok) throw new Error('模板文件不存在');
                return await response.json();
            } catch (error) {
                // 返回默认模板
                return {
                    c4: { 攻击力: 2.0, 暴击: 1.5, 暴击伤害: 1.5 },
                    c3: { 攻击力: 1.8, 暴击: 1.3, 暴击伤害: 1.3 },
                    c1: { 攻击力: 1.5, 暴击: 1.0, 暴击伤害: 1.0 }
                };
            }
        },
        
        switchTab(tab) {
            this.currentTab = tab;
            
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-tab') === tab);
            });
            
            this.updateTemplateDisplay();
        },
        
        updateTemplateDisplay() {
            if (!this.currentCharacter) {
                this.updateTemplateContent('请选择角色');
                return;
            }
            
            const templateData = this.currentCharacter.data[this.currentTab];
            if (!templateData) {
                this.updateTemplateContent('该角色暂无此费位模板');
                return;
            }
            
            const content = Object.entries(templateData)
                .map(([attr, weight]) => `<div class="template-item"><span>${attr}</span><span>${weight}</span></div>`)
                .join('');
            
            this.updateTemplateContent(`<div class="template-weights">${content}</div>`);
        },
        
        updateTemplateContent(content) {
            const containers = [
                document.getElementById('templateContent'),
                document.getElementById('mobileTemplateContent')
            ];
            
            containers.forEach(container => {
                if (container) container.innerHTML = content;
            });
        },
        
        applyTemplate() {
            if (!this.currentCharacter) {
                alert('请先选择角色');
                return;
            }
            
            const templateData = this.currentCharacter.data[this.currentTab];
            if (!templateData) {
                alert('该角色暂无此费位模板');
                return;
            }
            
            // 应用权重到表格
            Object.entries(templateData).forEach(([attr, weight]) => {
                // 查找对应的权重输入框
                const weightInputs = document.querySelectorAll('.table-input[id$="-weight"]');
                weightInputs.forEach(input => {
                    const typeSelect = document.getElementById(input.id.replace('-weight', '-type'));
                    if (typeSelect && typeSelect.value === attr) {
                        input.value = weight;
                    }
                });
            });
            
            // 重新计算总分
            if (typeof calculateTotal === 'function') {
                calculateTotal();
            }
            
            alert('模板已应用');
        },
        
        clearTemplate() {
            // 清除所有权重
            document.querySelectorAll('.table-input[id$="-weight"]').forEach(input => {
                input.value = '';
            });
            
            // 重新计算总分
            if (typeof calculateTotal === 'function') {
                calculateTotal();
            }
            
            alert('权重已清除');
        }
    };
    
    await templatesModule.init();
    return templatesModule;
}

// 懒加载模板功能
document.addEventListener('DOMContentLoaded', function() {
    const templateTriggers = [
        document.getElementById('characterSelect'),
        document.getElementById('mobileCharacterSelect'),
        document.getElementById('templateEntryBtn')
    ];
    
    templateTriggers.forEach(trigger => {
        if (trigger) {
            trigger.addEventListener('click', async function() {
                await loadTemplatesModule();
            }, { once: true });
        }
    });
});
