// 角色模板模块 - 修复版
let templatesModule = null;
let currentCharacterTemplate = null;
let currentTemplateTab = 'c4';

async function loadTemplatesModule() {
    if (templatesModule) return templatesModule;

    templatesModule = {
        characters: [],
        currentCharacter: null,
        currentTab: 'c4',

        async init() {
            await this.loadCharacters();
            this.bindEvents();
            this.populateCharacterSelects();
        },

        async loadCharacters() {
            try {
                // 从config.js获取角色列表
                if (typeof getCharacterList === 'function') {
                    this.characters = getCharacterList();
                } else if (window.CharacterConfig) {
                    this.characters = window.CharacterConfig.getCharacterList();
                } else {
                    // 备用角色列表
                    this.characters = ['安可', '白芷', '长离', '炽霞', '椿', '弗洛洛', '忌炎', '今汐', '卡卡罗', '卡提希娅'];
                }
                console.log('加载角色列表成功:', this.characters);
            } catch (error) {
                console.error('加载角色列表失败:', error);
                this.characters = ['安可', '白芷', '长离', '炽霞', '椿'];
            }
        },
        
        bindEvents() {
            // 桌面端角色选择
            const characterSelect = document.getElementById('characterSelect');
            characterSelect?.addEventListener('change', (e) => this.selectCharacter(e.target.value));
            
            // 移动端角色选择
            const mobileCharacterSelect = document.getElementById('mobileCharacterSelect');
            mobileCharacterSelect?.addEventListener('change', (e) => this.selectCharacter(e.target.value));
            
            // 标签页切换
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const tab = btn.getAttribute('data-tab');
                    this.switchTab(tab);
                });
            });
            
            // 应用模板按钮
            document.getElementById('applyTemplate')?.addEventListener('click', () => this.applyTemplate());
            document.getElementById('mobileApplyTemplate')?.addEventListener('click', () => this.applyTemplate());
            
            // 清除模板按钮
            document.getElementById('clearTemplate')?.addEventListener('click', () => this.clearTemplate());
            document.getElementById('mobileClearTemplate')?.addEventListener('click', () => this.clearTemplate());
        },
        
        populateCharacterSelects() {
            const selects = [
                document.getElementById('characterSelect'),
                document.getElementById('mobileCharacterSelect')
            ];
            
            selects.forEach(select => {
                if (select) {
                    select.innerHTML = '<option value="">请选择角色</option>';
                    this.characters.forEach(character => {
                        const option = document.createElement('option');
                        option.value = character;
                        option.textContent = character;
                        select.appendChild(option);
                    });
                }
            });
        },
        
        async selectCharacter(characterName) {
            if (!characterName) {
                this.currentCharacter = null;
                currentCharacterTemplate = null;
                this.updateTemplateContent('请选择角色');
                return;
            }

            try {
                console.log('正在加载角色模板:', characterName);
                const templateData = await this.loadCharacterTemplate(characterName);
                this.currentCharacter = { name: characterName, data: templateData };
                currentCharacterTemplate = templateData;
                currentTemplateTab = this.currentTab;
                console.log('角色模板加载成功:', templateData);
                this.updateTemplateDisplay();
            } catch (error) {
                console.error('加载角色模板失败:', error);
                this.updateTemplateContent(`加载模板失败: ${error.message}`);
            }
        },

        async loadCharacterTemplate(characterName) {
            try {
                console.log(`尝试加载: character/${characterName}/calc.json`);
                const response = await fetch(`character/${characterName}/calc.json`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                console.log('模板数据:', data);
                return data;
            } catch (error) {
                console.warn('使用默认模板，原因:', error.message);
                // 返回默认模板
                return {
                    main_props: {
                        "4": { "攻击%": 1.0, "暴击": 1.0, "暴击伤害": 1.0 },
                        "3": { "攻击%": 1.0, "暴击": 1.0, "暴击伤害": 1.0 },
                        "1": { "攻击%": 1.0, "暴击": 1.0, "暴击伤害": 1.0 }
                    },
                    sub_props: {
                        "攻击": 0.15, "攻击%": 0.8, "生命": 0.1, "生命%": 0.3,
                        "防御": 0.05, "防御%": 0.1, "暴击": 1.5, "暴击伤害": 1.0,
                        "共鸣效率": 0.4, "普攻伤害加成": 0.8, "重击伤害加成": 0.8,
                        "共鸣技能伤害加成": 0.8, "共鸣解放伤害加成": 0.8
                    }
                };
            }
        },
        
        switchTab(tab) {
            this.currentTab = tab;
            
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-tab') === tab);
            });
            
            this.updateTemplateDisplay();
        },
        
        updateTemplateDisplay() {
            if (!this.currentCharacter) {
                this.updateTemplateContent('请选择角色');
                return;
            }

            const templateData = this.currentCharacter.data;
            let displayData = null;

            // 根据当前标签页获取对应数据
            if (this.currentTab === 'c4' || this.currentTab === 'c3' || this.currentTab === 'c1') {
                const slotNumber = this.currentTab === 'c4' ? '4' :
                                 this.currentTab === 'c3' ? '3' : '1';

                // 显示主词条和副词条
                let content = '<div class="template-weights">';

                // 主词条
                if (templateData.main_props && templateData.main_props[slotNumber]) {
                    content += '<div class="template-section"><h4>主词条权重</h4>';
                    Object.entries(templateData.main_props[slotNumber]).forEach(([attr, weight]) => {
                        content += `<div class="template-item"><span>${attr}</span><span>${weight}</span></div>`;
                    });
                    content += '</div>';
                }

                // 副词条
                if (templateData.sub_props) {
                    content += '<div class="template-section"><h4>副词条权重</h4>';
                    Object.entries(templateData.sub_props).forEach(([attr, weight]) => {
                        if (weight > 0) {
                            content += `<div class="template-item"><span>${attr}</span><span>${weight}</span></div>`;
                        }
                    });
                    content += '</div>';
                }

                content += '</div>';
                this.updateTemplateContent(content);
            } else {
                this.updateTemplateContent('该角色暂无此费位模板');
            }
        },
        
        updateTemplateContent(content) {
            const containers = [
                document.getElementById('templateContent'),
                document.getElementById('mobileTemplateContent')
            ];
            
            containers.forEach(container => {
                if (container) container.innerHTML = content;
            });
        },
        
        applyTemplate() {
            if (!this.currentCharacter) {
                alert('请先选择角色');
                return;
            }

            const templateData = this.currentCharacter.data;
            if (!templateData) {
                alert('该角色暂无模板数据');
                return;
            }

            let appliedCount = 0;

            // 应用副词条权重
            if (templateData.sub_props) {
                const attributeMapping = {
                    '攻击': 'atk', '攻击%': 'atk_percent',
                    '生命': 'hp', '生命%': 'hp_percent',
                    '防御': 'def', '防御%': 'def_percent',
                    '暴击': 'crit_rate', '暴击伤害': 'crit_damage',
                    '共鸣效率': 'resonance_efficiency',
                    '普攻伤害加成': 'basic_attack_damage',
                    '重击伤害加成': 'heavy_attack_damage',
                    '共鸣技能伤害加成': 'resonance_skill_damage',
                    '共鸣解放伤害加成': 'resonance_liberation_damage'
                };

                // 应用到副词条
                ['sub-1', 'sub-2', 'sub-3', 'sub-4', 'sub-5'].forEach(entryId => {
                    const attributeSelect = document.getElementById(`${entryId}-attribute`);
                    const weightInput = document.getElementById(`${entryId}-weight`);

                    if (attributeSelect && weightInput && attributeSelect.value) {
                        const selectedAttr = attributeSelect.value;

                        // 查找对应的中文名称
                        const chineseName = Object.keys(attributeMapping).find(key =>
                            attributeMapping[key] === selectedAttr
                        );

                        if (chineseName && templateData.sub_props[chineseName] !== undefined) {
                            weightInput.value = templateData.sub_props[chineseName];
                            appliedCount++;
                        }
                    }
                });
            }

            // 重新计算总分
            if (typeof calculateTotal === 'function') {
                calculateTotal();
            } else if (typeof updateEntry === 'function') {
                ['sub-1', 'sub-2', 'sub-3', 'sub-4', 'sub-5'].forEach(entryId => {
                    updateEntry(entryId);
                });
            }

            alert(`模板已应用，更新了 ${appliedCount} 个词条的权重`);
        },
        
        clearTemplate() {
            // 清除所有权重
            document.querySelectorAll('.table-input[id$="-weight"]').forEach(input => {
                input.value = '';
            });
            
            // 重新计算总分
            if (typeof calculateTotal === 'function') {
                calculateTotal();
            }
            
            alert('权重已清除');
        }
    };
    
    await templatesModule.init();
    return templatesModule;
}

// 懒加载模板功能
document.addEventListener('DOMContentLoaded', function() {
    const templateTriggers = [
        document.getElementById('characterSelect'),
        document.getElementById('mobileCharacterSelect'),
        document.getElementById('templateEntryBtn')
    ];

    templateTriggers.forEach(trigger => {
        if (trigger) {
            trigger.addEventListener('click', async function() {
                window.templatesModule = await loadTemplatesModule();
            }, { once: true });

            // 对于select元素，也监听change事件
            if (trigger.tagName === 'SELECT') {
                trigger.addEventListener('change', async function() {
                    if (!window.templatesModule) {
                        window.templatesModule = await loadTemplatesModule();
                    }
                }, { once: true });
            }
        }
    });

    // 预加载模板功能
    setTimeout(async () => {
        try {
            window.templatesModule = await loadTemplatesModule();
            console.log('模板模块预加载完成');
        } catch (error) {
            console.error('模板模块预加载失败:', error);
        }
    }, 1500);
});
